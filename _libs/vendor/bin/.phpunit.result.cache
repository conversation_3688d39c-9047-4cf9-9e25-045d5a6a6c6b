{"version": 1, "defects": [], "times": {"Tests\\Nzoom\\Export\\DataFactoryTest::testTableProviderWithModels": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testTableProviderErrorHandling": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testConstructor": 0.002, "Tests\\Nzoom\\Export\\DataFactoryTest::testSetChunkSize": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithEmptyModels": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithOutlookFields": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithModels": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testFieldTypeMapping": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithLargeModelSet": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateStreaming": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateStreamingWithDefaultPageSize": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateStreamingWithComplexFilters": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreaming": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testHeaderCreationWithDefaultValues": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testModelWithoutGetExportVarTypeMethod": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testComplexScenario": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testStreamingMethodsPreserveFilterStructure": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateStreamingRecordProviderExecution": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testStreamingPaginationBehavior": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testFieldsWithEmptyPositionsAreFiltered": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testFieldsWithEmptyPositionsInStreamingMethods": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testCursorStreamingPaginationBehavior": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testSetTableProvider": 0.003, "Tests\\Nzoom\\Export\\DataFactoryTest::testWithModelTableProvider": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testWithModelTableProviderDefaultOptions": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testWithModelTableProviderFluent": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testTableProviderWithStreamingMethods": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testMultipleTableProviderConfigurations": 0}}